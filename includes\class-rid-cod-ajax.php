<?php
/**
 * AJAX handler class
 */
class RID_COD_Ajax {

    /**
     * Constructor
     */
    public function __construct() {
        // Register AJAX actions
        add_action('wp_ajax_rid_cod_place_order', array($this, 'process_order'));
        add_action('wp_ajax_nopriv_rid_cod_place_order', array($this, 'process_order'));

        add_action('wp_ajax_rid_cod_get_cities', array($this, 'get_cities'));
        add_action('wp_ajax_nopriv_rid_cod_get_cities', array($this, 'get_cities'));

        // Action for saving draft/abandoned order
        add_action('wp_ajax_rid_cod_save_draft_order', array($this, 'save_draft_order'));
        add_action('wp_ajax_nopriv_rid_cod_save_draft_order', array($this, 'save_draft_order'));

        // Action hook for the scheduled Google Sheets sync
        add_action('rid_cod_schedule_google_sheets_sync', array($this, 'handle_scheduled_google_sheets_sync'), 10, 2);

        // Filter to prevent duplicate variation meta in order display
        add_filter('woocommerce_order_item_display_meta_key', array($this, 'filter_order_item_meta_key'), 10, 3);

        // Testing action for Google Sheets data extraction
        add_action('wp_ajax_rid_cod_test_google_sheets_data', array($this, 'test_google_sheets_data_extraction'));
        add_action('wp_ajax_nopriv_rid_cod_test_google_sheets_data', array($this, 'test_google_sheets_data_extraction'));

        // Testing action for Google Sheets live sending (admin only)
        add_action('wp_ajax_rid_cod_test_google_sheets_send', array($this, 'test_google_sheets_send'));

        // States management actions (admin only)
        add_action('wp_ajax_rid_cod_get_states', array($this, 'get_states_for_country'));
        add_action('wp_ajax_rid_cod_save_states', array($this, 'save_states_for_country'));
        add_action('wp_ajax_rid_cod_add_state', array($this, 'add_state'));
        add_action('wp_ajax_rid_cod_edit_state', array($this, 'edit_state'));
        add_action('wp_ajax_rid_cod_delete_state', array($this, 'delete_state'));
        add_action('wp_ajax_rid_cod_reset_states', array($this, 'reset_states_to_default'));
    }

    /**
     * Process the order
     */
    public function process_order() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rid_cod_form_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'rid-cod')));
        }

        // Validate required fields
        $required_fields = array('product_id', 'full_name', 'phone');

        // Check form control settings for conditional required fields
        $show_states = get_option('rid_cod_show_states', 'yes') === 'yes';
        $show_cities = get_option('rid_cod_show_cities', 'yes') === 'yes';

        if ($show_states) {
            $required_fields[] = 'state';
        }
        if ($show_cities) {
            $required_fields[] = 'city';
        }

        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                // Provide better error messages in Arabic
                $field_names = array(
                    'product_id' => __('المنتج', 'rid-cod'),
                    'full_name' => __('الاسم الكامل', 'rid-cod'),
                    'phone' => __('رقم الهاتف', 'rid-cod'),
                    'state' => __('الولاية', 'rid-cod'),
                    'city' => __('البلدية', 'rid-cod')
                );
                $field_name = isset($field_names[$field]) ? $field_names[$field] : $field;
                wp_send_json_error(array('message' => sprintf(__('الرجاء ملء حقل %s', 'rid-cod'), $field_name)));
            }
        }

        // Validate phone number format if enabled
        $validate_phone = get_option('rid_cod_enable_phone_validation', 'yes') === 'yes';
        if ($validate_phone) {
            $phone_number = isset($_POST['phone']) ? preg_replace('/\s+/', '', sanitize_text_field($_POST['phone'])) : ''; // Remove spaces
            $selected_country = get_option('rid_cod_selected_country', 'DZ');
            
            // Get country manager instance
            if (class_exists('RID_COD_Country_Manager')) {
                if (!RID_COD_Country_Manager::validate_phone($phone_number, $selected_country)) {
                    $country_data = RID_COD_Country_Manager::get_country_data($selected_country);
                    $country_name = $country_data ? $country_data['name'] : 'صحيح';
                    $phone_example = RID_COD_Country_Manager::get_phone_example($selected_country);
                    
                    $error_message = sprintf(__('الرجاء إدخال رقم هاتف %s صحيح. مثال: %s', 'rid-cod'), $country_name, $phone_example);
                    wp_send_json_error(array('message' => $error_message));
                }
            }
        }

        // Get product information
        $product_id = absint($_POST['product_id']);
        $parent_product = wc_get_product($product_id);

        if (!$parent_product) {
            wp_send_json_error(array('message' => __('المنتج المحدد غير موجود', 'rid-cod')));
        }

        // Get variation if applicable
        $variation_id = isset($_POST['variation_id']) ? absint($_POST['variation_id']) : 0;
        $variation_data = array();

        // Check if product has variations and validate variation
        // Check if product has variations and validate variation
        // Use $parent_product which is defined, not $product
        if ($parent_product->is_type('variable')) {
            if ($variation_id === 0) {
                wp_send_json_error(array('message' => __('الرجاء اختيار نوع المنتج', 'rid-cod')));
            }

            $variation_product = wc_get_product($variation_id);
            if (!$variation_product || !$variation_product->is_type('variation')) {
                wp_send_json_error(array('message' => __('نوع المنتج المحدد غير صحيح', 'rid-cod')));
            }

            // Verify variation belongs to the parent product
            // Use $parent_product which is defined, not $product
            if ($variation_product->get_parent_id() !== $parent_product->get_id()) {
                wp_send_json_error(array('message' => __('هذا الخيار لا ينتمي للمنتج المحدد', 'rid-cod')));
            }

            if (!$variation_product->is_purchasable()) {
                $error_msg = __('هذا الخيار غير متاح حاليًا', 'rid-cod');
                if ($variation_product->backorders_allowed()) {
                    $error_msg = __('هذا الخيار متاح للطلب المسبق', 'rid-cod');
                } elseif (!$variation_product->is_in_stock()) {
                    $error_msg = __('هذا الخيار غير متوفر في المخزون', 'rid-cod');
                }
                wp_send_json_error(array('message' => $error_msg));
            }            // Enhanced validation for variation attributes with URL decoding
            $variation_attributes = $variation_product->get_variation_attributes();
            $parent_attributes = $parent_product->get_variation_attributes();

            // Get submitted attributes from form data for cross-validation
            $submitted_attributes = array();
            foreach ($_POST as $key => $value) {
                if (strpos($key, 'attribute_') === 0 && !empty($value)) {
                    // Decode the submitted value to handle Arabic text
                    $decoded_value = rid_cod_decode_variation_attribute($value);
                    $submitted_attributes[$key] = sanitize_text_field($decoded_value);
                    
                    // Log the decoding for debugging
                    if ($value !== $decoded_value) {
                        error_log("RID COD AJAX: Decoded attribute '$key': '$value' -> '$decoded_value'");
                    }
                }
            }

            $valid_variation = true;
            $missing_attributes = array();

            // Validate that all required attributes are properly selected
            foreach ($parent_attributes as $attribute_name => $attribute_options) {
                $attribute_key = 'attribute_' . sanitize_title($attribute_name);
                
                // Decode variation attribute value
                $variation_value = isset($variation_attributes[$attribute_name]) ? 
                    rid_cod_decode_variation_attribute($variation_attributes[$attribute_name]) : '';
                    
                $submitted_value = isset($submitted_attributes[$attribute_key]) ? 
                    $submitted_attributes[$attribute_key] : '';

                // Check if this is a required attribute (has multiple options)
                $is_required = is_array($attribute_options) && count($attribute_options) > 1;

                if ($is_required) {
                    // If variation has empty value but we have submitted value, use submitted
                    if (empty($variation_value) && !empty($submitted_value)) {
                        // Decode all attribute options for comparison
                        $decoded_options = array_map('rid_cod_decode_variation_attribute', $attribute_options);
                        
                        // Verify submitted value is valid for this attribute
                        if (in_array($submitted_value, $decoded_options) || in_array($submitted_value, $attribute_options)) {
                            // Update variation attributes with submitted value
                            $variation_attributes[$attribute_name] = $submitted_value;
                        } else {
                            $valid_variation = false;
                            // Use the new helper function to get properly decoded attribute label
                            $decoded_label = $this->get_decoded_attribute_label($attribute_name);
                            $missing_attributes[] = $decoded_label;
                            error_log("RID COD AJAX: Invalid attribute value '$submitted_value' for '$attribute_name'");
                        }
                    } elseif (empty($variation_value) && empty($submitted_value)) {
                        // Both are empty for required attribute
                        $valid_variation = false;
                        // Use the new helper function to get properly decoded attribute label
                        $decoded_label = $this->get_decoded_attribute_label($attribute_name);
                        $missing_attributes[] = $decoded_label;
                        error_log("RID COD AJAX: Missing required attribute '$attribute_name'");
                    }
                }
            }

            // Only reject if we have actually missing required attributes
            if (!$valid_variation && !empty($missing_attributes)) {
                // Log detailed information for debugging
                error_log("RID COD AJAX: Validation failed. Missing attributes: " . implode(', ', $missing_attributes));
                
                $error_message = sprintf(
                    __('الرجاء اختيار: %s', 'rid-cod'),
                    implode(', ', $missing_attributes)
                );
                wp_send_json_error(array('message' => $error_message));
            }
              // Store enhanced variation data for later use
            // Format variation data properly for WooCommerce
            $variation_data = array();
            foreach ($variation_attributes as $attribute_name => $attribute_value) {
                if (!empty($attribute_value)) {
                    // Format the attribute key correctly for WooCommerce
                    $attribute_key = 'attribute_' . sanitize_title($attribute_name);
                    $variation_data[$attribute_key] = $attribute_value;
                }
            }

            // Log variation data for debugging
            error_log('RID COD: Final variation data prepared: ' . print_r($variation_data, true));

            $product = $parent_product; // Use parent product for order creation
        }
        // For simple products, check stock
        elseif (!$parent_product->is_in_stock()) {
            wp_send_json_error(array('message' => __('المنتج غير متوفر حالياً', 'rid-cod')));
        }

        // Get quantity
        $quantity = isset($_POST['quantity']) ? absint($_POST['quantity']) : 1;

        // Prepare order data for validation
        $order_data = array(
            'phone' => isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '',
            'full_name' => isset($_POST['full_name']) ? sanitize_text_field($_POST['full_name']) : '',
            'product_id' => $product_id,
            'quantity' => $quantity
        );

        // Hook: Check order limits before creating order (for 24-hour prevention)
        try {
            do_action('rid_cod_before_order_creation', $order_data);
        } catch (Exception $e) {
            wp_send_json_error(array('message' => $e->getMessage()));
        }

        // Create a new order
        $order = wc_create_order();          // Prepare arguments for adding product with variation data
        $add_product_args = array();

        if ($variation_id > 0 && $parent_product->is_type('variable')) {
            // Prepare variation arguments for WooCommerce order creation
            $add_product_args = array(
                'variation_id' => $variation_id,
                'attributes'   => $variation_data
            );

            // Log variation data for debugging
            error_log('RID COD: Preparing to add variation to order with data: ' . print_r($variation_data, true));
        }// Add product to order with enhanced variation handling
        if ($variation_id > 0) {
            // Add the actual variation product with enhanced attributes
            // WooCommerce automatically handles variation meta when we pass the variation array
            $item_id = $order->add_product(
                $variation_product,
                $quantity,
                array(
                    'variation' => $variation_data
                )
            );

            // Log successful addition for debugging
            if ($item_id) {
                error_log('RID COD: Successfully added variation product to order. Item ID: ' . $item_id . ', Variation ID: ' . $variation_id);
                error_log('RID COD: Variation data automatically handled by WooCommerce: ' . print_r($variation_data, true));
            }
        } else {
            // Add simple product
            $order->add_product(
                $parent_product,
                $quantity
            );
        }

        // Get full state name if sent from JS
        $state_full_name = isset($_POST['state_full_name']) ? sanitize_text_field($_POST['state_full_name']) : sanitize_text_field($_POST['state']); // Fallback to state code if full name not sent

        // Set billing information
        $order->set_billing_first_name(sanitize_text_field($_POST['full_name']));
        $order->set_billing_phone(sanitize_text_field($_POST['phone']));
        $order->set_billing_state($state_full_name); // Use full state name
        $order->set_billing_city(sanitize_text_field($_POST['city']));

        if (isset($_POST['address'])) {
            $order->set_billing_address_1(sanitize_text_field($_POST['address']));
        }

        // Set shipping information (same as billing)
        $order->set_shipping_first_name(sanitize_text_field($_POST['full_name']));
        $order->set_shipping_state($state_full_name); // Use full state name
        $order->set_shipping_city(sanitize_text_field($_POST['city']));

        if (isset($_POST['address'])) {
            $order->set_shipping_address_1(sanitize_text_field($_POST['address']));
        }

        // Add shipping
        $shipping_cost = isset($_POST['shipping_cost']) ? floatval($_POST['shipping_cost']) : 0;
        if ($shipping_cost > 0) {
            $shipping_item = new WC_Order_Item_Shipping();
            $shipping_item->set_method_title(__('Shipping', 'rid-cod'));
            $shipping_item->set_total($shipping_cost);
            $order->add_item($shipping_item);
        }

        // Add order notes
        $order_notes = '';
        if (!empty($_POST['order_notes'])) {
            $order_notes .= sanitize_textarea_field($_POST['order_notes']) . "\n";
        }

        // Add delivery type to notes if submitted
        if (isset($_POST['delivery_type'])) {
            $delivery_type = sanitize_text_field($_POST['delivery_type']);
            $home_label = get_option('rid_cod_delivery_type_home_label', __('توصيل للمنزل', 'rid-cod'));
            $desk_label = get_option('rid_cod_delivery_type_desk_label', __('توصيل للمكتب', 'rid-cod'));
            $delivery_type_label = ($delivery_type === 'desk') ? $desk_label : $home_label; // Default to home
            $order_notes .= __('نوع التوصيل المفضل:', 'rid-cod') . ' ' . $delivery_type_label;

            // Store raw delivery type as order meta data for reliability
            if ($order_id = $order->get_id()) { // Ensure order ID exists before saving meta
                 update_post_meta($order_id, '_rid_cod_delivery_type', $delivery_type);
            }
        }

        if (!empty(trim($order_notes))) {
             $order->add_order_note(trim($order_notes), 0, false); // Add as internal note initially
        }

        // Set payment method to COD
        $order->set_payment_method('cod');
        $order->set_payment_method_title(__('Cash on Delivery', 'rid-cod'));

        // Set order status
        $order->set_status('processing');

        // Calculate totals
        $order->calculate_totals();

        // Save the order
        // Save the order (returns order ID)
        $order_id = $order->save();

        // Re-check if meta needs saving (in case ID wasn't available before save)
        if ($order_id && isset($_POST['delivery_type'])) {
             // Check if meta was already saved, if not, save it now.
             if (!get_post_meta($order_id, '_rid_cod_delivery_type', true)) {
                 update_post_meta($order_id, '_rid_cod_delivery_type', sanitize_text_field($_POST['delivery_type']));
             }
        }
        if ($order_id) {
            // Send order confirmation email
            WC()->mailer()->emails['WC_Email_Customer_Processing_Order']->trigger($order_id);

            // --- Schedule Google Sheets Sync ---
            $google_sheets_options = get_option('rid_cod_gs_options');
            if ( isset($google_sheets_options['enable_google_sheets']) && $google_sheets_options['enable_google_sheets'] === 'yes' && !empty($google_sheets_options['apps_script_url']) ) {
                // Schedule the sync to run 5 seconds after the order is processed.
                // Pass order ID and options to the scheduled action.
                wp_schedule_single_event(time() + 5, 'rid_cod_schedule_google_sheets_sync', array($order_id, $google_sheets_options));
            }
            // --- End Schedule Google Sheets Sync ---

            // Hook: Record order for 24-hour limit checking
            do_action('rid_cod_after_order_creation', $order_id, $order_data);

            // Return success
            // Get the custom success message from settings, with a default fallback
            $success_message = get_option('rid_cod_success_message', __('تم الطلب بنجاح', 'rid-cod'));

            wp_send_json_success(array(
                'message' => $success_message, // Use the retrieved or default message
                'order_id' => $order_id,
                'redirect' => $order->get_checkout_order_received_url()
            ));
        } else {
            wp_send_json_error(array('message' => __('Failed to create order', 'rid-cod')));
        }
    }

    /**
     * Get cities for a state
     */
    public function get_cities() {
        // Check nonce (using the general nonce passed to JS)
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rid_cod_nonce')) {
             wp_send_json_error(array('message' => __('Security check failed', 'rid-cod')));
        }

        $state = isset($_POST['state']) ? sanitize_text_field($_POST['state']) : '';

        if (empty($state)) {
            wp_send_json_error(array('message' => __('No state provided', 'rid-cod')));
        }

        // Include Algeria states and cities data
        $current_country = RID_COD_Country_Manager::get_current_country();

        // Get cities for the selected state
        $cities = RID_COD_Country_Manager::get_cities_by_state($current_country, $state);

        // Add cache headers to improve performance
        header('Cache-Control: max-age=3600, must-revalidate');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');

        if (!empty($cities)) {
            wp_send_json_success(array('cities' => $cities));
        } else {
            wp_send_json_error(array('message' => __('No cities found for this state', 'rid-cod')));
        }
    }

    /**
     * Handles the scheduled WP Cron event to send order data to Google Sheets.
     * This runs in the background and doesn't affect the user's request time.
     *
     * @param int   $order_id The ID of the order to sync.
     * @param array $google_sheets_options The Google Sheets settings passed during scheduling.
     */
    public function handle_scheduled_google_sheets_sync($order_id, $google_sheets_options) {
        $order = wc_get_order($order_id);

        if (!$order) {
            error_log("RID COD Google Sheets Cron Error: Could not retrieve order ID: $order_id");
            return;
        }

        $apps_script_url = $google_sheets_options['apps_script_url'] ?? '';
        if (empty($apps_script_url)) {
            error_log('RID COD Google Sheets Cron Error: Apps Script URL is not configured for order ID: ' . $order_id);
            return;
        }        // --- Reconstruct Order Data for Sheets ---
        $items = $order->get_items();
        $first_item = reset($items);
        $product_for_sheets = $first_item ? $first_item->get_product() : null;

        $product_id_for_sheets = $product_for_sheets ? $product_for_sheets->get_id() : 0; // Get product/variation ID
        $product_name_for_sheets = $first_item ? $first_item->get_name() : ''; // Get item name (includes variation details)
        $quantity_for_sheets = $first_item ? $first_item->get_quantity() : 1;

        // Enhanced logging for debugging product data
        error_log('RID COD Google Sheets: Product for sheets - ID: ' . $product_id_for_sheets . ', Type: ' . ($product_for_sheets ? $product_for_sheets->get_type() : 'null'));

        // If we have a variation product, also log its attributes for debugging
        if ($product_for_sheets && $product_for_sheets->is_type('variation')) {
            $variation_attrs = $product_for_sheets->get_variation_attributes();
            error_log('RID COD Google Sheets: Variation attributes from product: ' . print_r($variation_attrs, true));

            // Also get meta data for additional debugging
            $item_meta = $first_item->get_meta_data();
            error_log('RID COD Google Sheets: Item meta data: ' . print_r($item_meta, true));
        }

        // Get delivery type: Check if feature is enabled, then read from order meta
        $enable_delivery_type_option = get_option('rid_cod_enable_delivery_type', 'no');
        $delivery_type_for_sheets = ''; // Initialize with empty string (safer default)

        if ($enable_delivery_type_option === 'yes') {
            // Feature is enabled, try to get the raw value ('home' or 'desk') from order meta
            $raw_delivery_type = get_post_meta($order_id, '_rid_cod_delivery_type', true);

            if (!empty($raw_delivery_type)) {
                // Get the corresponding display label based on the raw value
                $home_label = get_option('rid_cod_delivery_type_home_label', __('توصيل للمنزل', 'rid-cod'));
                $desk_label = get_option('rid_cod_delivery_type_desk_label', __('توصيل للمكتب', 'rid-cod'));
                $delivery_type_for_sheets = ($raw_delivery_type === 'desk') ? $desk_label : $home_label; // Default to home label if not 'desk'
            }
            // If meta is empty or not found, $delivery_type_for_sheets remains ''
        }
        // If feature is disabled ($enable_delivery_type_option === 'no'), $delivery_type_for_sheets remains ''        // Enhanced Color, Size and Width attributes extraction using helper function
        // Get the actual selected attributes from the order items
        $order_item_meta = array();
        if ($first_item) {
            $item_meta = $first_item->get_meta_data();
            foreach ($item_meta as $meta) {
                $order_item_meta[$meta->key] = $meta->value;
            }
        }

        $extracted_attributes = $this->extract_variation_attributes_for_sheets($product_for_sheets, $order_item_meta);
        $color_for_sheets = $extracted_attributes['color'];
        $size_for_sheets = $extracted_attributes['size'];
        $width_for_sheets = $extracted_attributes['width'];// Prepare enhanced data array with improved attribute handling
        $order_data_for_sheets = array(
            'product_id'    => $product_id_for_sheets,
            'product_name'  => $product_name_for_sheets,
            'quantity'      => $quantity_for_sheets,
            'color'         => $color_for_sheets,
            'size'          => $size_for_sheets,
            'width'         => $width_for_sheets, // New field for other attributes
            'customer_name' => $order->get_billing_first_name(),
            'phone'         => $order->get_billing_phone(),
            'state'         => $order->get_billing_state(), // Get state saved in order
            'city'          => $order->get_billing_city(),
            'delivery_type' => $delivery_type_for_sheets,
            'product_price' => $product_for_sheets ? $order->get_item_subtotal($first_item, false, false) : 'N/A',
            'shipping_cost' => $order->get_shipping_total(),
            'total_price'   => $order->get_total(),
            // 'order_date' is handled by Apps Script
        );

        // Enhanced logging for Google Sheets data
        error_log('RID COD Google Sheets: Prepared data for order ' . $order_id . ': ' . print_r($order_data_for_sheets, true));

        // Add sheet name if configured
        if (!empty($google_sheets_options['sheet_name'])) {
            $order_data_for_sheets['target_sheet'] = $google_sheets_options['sheet_name'];
        }
        // --- End Reconstruct Order Data ---

        // --- Send Data via wp_remote_post ---
        $args = array(
            'body'        => json_encode($order_data_for_sheets),
            'headers'     => array('Content-Type' => 'application/json'),
            'timeout'     => 30, // Increase timeout slightly for background task
            'redirection' => 5,
            'blocking'    => true, // Blocking is okay for background tasks
            'httpversion' => '1.0',
            'sslverify'   => true,
            'data_format' => 'body',
        );

        $response = wp_remote_post($apps_script_url, $args);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log("RID COD Google Sheets Cron Error (wp_remote_post) for Order ID $order_id: " . $error_message);
        } else {
            $response_code = wp_remote_retrieve_response_code($response);
            if (!($response_code >= 200 && $response_code < 400)) { // Check for non-success codes
                $response_body = wp_remote_retrieve_body($response);
                error_log("RID COD Google Sheets Cron Error (Request Failed) for Order ID $order_id: Response Code: $response_code Body: $response_body");
            } else {
                // Optional: Log success for debugging cron jobs
                // error_log("RID COD Google Sheets Cron: Successfully sent data for Order ID $order_id. Response code: $response_code");
            }
        }
        // --- End Send Data ---
    }

    /**
     * Save partial form data as a draft order (status: pending).
     */
    public function save_draft_order() {
        // Check nonce (use the general nonce passed to JS)
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rid_cod_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'rid-cod')));
        }

        // Basic validation: Need at least product_id, name, and phone
        $product_id = isset($_POST['product_id']) ? absint($_POST['product_id']) : 0;
        $full_name = isset($_POST['full_name']) ? sanitize_text_field($_POST['full_name']) : '';
        $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';

        if (empty($product_id) || empty($full_name) || empty($phone)) {
            wp_send_json_error(array('message' => __('Missing required draft data (product, name, phone)', 'rid-cod')));
        }

        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(array('message' => __('Invalid product for draft', 'rid-cod')));
        }

        $draft_order_id = isset($_POST['draft_order_id']) ? absint($_POST['draft_order_id']) : 0;
        $order = null;

        // Try to load existing draft order if ID is provided and it's still pending
        if ($draft_order_id > 0) {
            $existing_order = wc_get_order($draft_order_id);
            // Only update if it exists and is still in 'pending' status
            if ($existing_order && $existing_order->has_status('pending')) {
                $order = $existing_order;
                // Clear existing items before adding potentially updated ones
                $order->remove_order_items('line_item');
                $order->remove_order_items('shipping');
            }
        }

        // If no valid existing order, create a new one
        if (!$order) {
            $order = wc_create_order();
            if (is_wp_error($order)) {
                 wp_send_json_error(array('message' => __('Failed to create draft order object', 'rid-cod')));
            }
            // Add a note indicating this is an auto-saved draft
            $order->add_order_note(__('طلب متروك - تم إنشاؤه تلقائيًا.', 'rid-cod'), 0, false);
        }

        // --- Populate Order Data ---

        // Get variation if applicable
        $variation_id = isset($_POST['variation_id']) ? absint($_POST['variation_id']) : 0;
        $product_to_add = $product; // Default to main product
        $add_product_args = array();

        if ($variation_id > 0) {
            $variation_product = wc_get_product($variation_id);
            if ($variation_product && $variation_product->is_type('variation')) {
                $product_to_add = $variation_product;
                $variation_data = array();
                $variation_attributes = $variation_product->get_variation_attributes();
                foreach ($variation_attributes as $attribute_taxonomy => $attribute_slug) {
                    if (!empty($attribute_slug)) {
                        $variation_data[$attribute_taxonomy] = $attribute_slug;
                    }
                }
                if (!empty($variation_data)) {
                    $add_product_args['variation'] = $variation_data;
                }
            }
        }

        // Get quantity
        $quantity = isset($_POST['quantity']) ? absint($_POST['quantity']) : 1;

        // Add product to order
        $order->add_product($product_to_add, $quantity, $add_product_args);

        // Get full state name if available
        $state_code = isset($_POST['state']) ? sanitize_text_field($_POST['state']) : '';
        $state_full_name = isset($_POST['state_full_name']) ? sanitize_text_field($_POST['state_full_name']) : $state_code; // Use full name if sent, else code

        // Set billing information (only essential for draft)
        $order->set_billing_first_name($full_name);
        $order->set_billing_phone($phone);
        if (!empty($state_full_name)) {
            $order->set_billing_state($state_full_name);
        }
        if (!empty($_POST['city'])) {
            $order->set_billing_city(sanitize_text_field($_POST['city']));
        }
        // Maybe add address later if needed

        // Set shipping information (mirror billing for draft)
        $order->set_shipping_first_name($full_name);
        if (!empty($state_full_name)) {
            $order->set_shipping_state($state_full_name);
        }
        if (!empty($_POST['city'])) {
            $order->set_shipping_city(sanitize_text_field($_POST['city']));
        }

        // Add shipping cost if available
        $shipping_cost = isset($_POST['shipping_cost']) ? floatval($_POST['shipping_cost']) : 0;
        if ($shipping_cost >= 0) { // Allow 0 for free shipping
            $shipping_item = new WC_Order_Item_Shipping();
            $shipping_item->set_method_title(__('Shipping', 'rid-cod')); // Generic title for draft
            $shipping_item->set_total($shipping_cost);
            $order->add_item($shipping_item);
        }

        // Set payment method to COD (as it's the context)
        $order->set_payment_method('cod');
        $order->set_payment_method_title(__('Cash on Delivery', 'rid-cod'));

        // Set status to 'pending' (standard for unpaid/incomplete orders)
        $order->set_status('pending');

        // Calculate totals
        $order->calculate_totals();

        // Save the draft order
        $new_order_id = $order->save();

        if ($new_order_id) {
            // Return the ID of the created/updated draft order
            wp_send_json_success(array(
                'message' => __('Draft order saved/updated.', 'rid-cod'),
                'draft_order_id' => $new_order_id
            ));
        } else {
            wp_send_json_error(array('message' => __('Failed to save draft order.', 'rid-cod')));
        }
    }

    /**
     * Filter order item meta keys to prevent duplicate variation information
     * This ensures that variation attributes don't appear twice in order summaries
     */
    public function filter_order_item_meta_key($display_key, $meta, $item) {
        // Keep track of displayed meta keys to prevent duplicates
        static $displayed_keys = array();

        // Generate a unique identifier for this order item
        $item_id = $item->get_id();
        $meta_key = $meta->key;
        $meta_value = $meta->value;

        // Create unique combination identifier
        $unique_key = $item_id . '_' . $meta_key . '_' . $meta_value;

        // If this exact combination has already been displayed, hide it
        if (in_array($unique_key, $displayed_keys)) {
            return false; // Hide duplicate meta
        }

        // Add to displayed keys list
        $displayed_keys[] = $unique_key;

        // For attribute meta, make sure we use proper display names
        if (strpos($meta_key, 'attribute_') === 0 || strpos($meta_key, 'pa_') !== false) {
            // Try to get proper attribute label
            $attribute_name = str_replace('attribute_', '', $meta_key);
            $attribute_label = wc_attribute_label($attribute_name);

            if ($attribute_label && $attribute_label !== $attribute_name) {
                return $attribute_label;
            }
        }

        return $display_key;
    }

    /**
     * Helper function to extract variation attributes systematically
     * This function can be used for testing and debugging variation data extraction
     *
     * @param WC_Product_Variation $variation_product
     * @param array $order_item_meta Optional order item meta data with selected attributes
     * @return array Array with color, size, and other attributes
     */    private function extract_variation_attributes_for_sheets($variation_product, $order_item_meta = array()) {
        if (!$variation_product) {
            return array(
                'color' => 'لا يدعم',
                'size' => 'لا يدعم',
                'width' => 'لا يدعم'
            );
        }

        $variation_attributes = array();

        // Enhanced approach: Get attributes based on product type
        if ($variation_product->is_type('variation')) {
            // For variation products, get variation attributes
            $variation_attributes = $variation_product->get_variation_attributes();
            error_log('RID COD Helper: Variation attributes from variation product: ' . print_r($variation_attributes, true));
        } else {
            // For simple products or other types, return defaults
            error_log('RID COD Helper: Product is not a variation, returning defaults');
            return array(
                'color' => 'لا يدعم',
                'size' => 'لا يدعم',
                'width' => 'لا يدعم'
            );
        }

        // Enhanced logging for debugging
        error_log('RID COD Helper: Raw variation attributes: ' . print_r($variation_attributes, true));
        error_log('RID COD Helper: Order item meta: ' . print_r($order_item_meta, true));

        // Merge order item meta with variation attributes (order meta takes priority)
        $combined_attributes = array_merge($variation_attributes, $order_item_meta);

        // Log each attribute name and value for debugging
        foreach ($combined_attributes as $attr_name => $attr_value) {
            error_log('RID COD Helper: Checking combined attribute: ' . $attr_name . ' = ' . $attr_value);
        }

        // Use combined attributes for extraction
        $variation_attributes = $combined_attributes;

        // Helper function to get display value for attribute
        $get_display_value = function($attr_name, $attr_value) {
            // If it's a taxonomy attribute, try to get the term name
            if (strpos($attr_name, 'pa_') !== false) {
                $taxonomy = str_replace('attribute_', '', $attr_name);
                $term = get_term_by('slug', $attr_value, $taxonomy);
                if ($term && !is_wp_error($term)) {
                    // Decode the term name to handle Arabic characters properly
                    return rid_cod_decode_variation_attribute($term->name);
                }
                // If term not found by slug, try by name
                $term = get_term_by('name', $attr_value, $taxonomy);
                if ($term && !is_wp_error($term)) {
                    // Decode the term name to handle Arabic characters properly
                    return rid_cod_decode_variation_attribute($term->name);
                }
            }
            // Return decoded value if no term found
            return rid_cod_decode_variation_attribute($attr_value);
        };

        // Helper function to normalize attribute names for better matching
        $normalize_attr_name = function($attr_name) {
            $normalized = strtolower(trim($attr_name));
            // Remove common prefixes
            $normalized = str_replace(array('attribute_pa_', 'attribute_', 'pa_'), '', $normalized);
            // Remove special characters and spaces but keep Arabic characters
            $normalized = preg_replace('/[^a-z0-9؀-ۿݐ-ݿࢠ-ࣿﭐ-﷿ﹰ-﻿]/u', '', $normalized);
            return $normalized;
        };

        // Helper function to check if a value looks like a color (more precise)
        $is_color_value = function($value) {
            $value_lower = strtolower(trim($value));

            // Exact color matches (more precise)
            $exact_colors = array(
                // English colors
                'red', 'blue', 'green', 'black', 'white', 'yellow', 'pink', 'purple', 'orange', 'brown', 'gray', 'grey', 'silver', 'gold',
                'navy', 'maroon', 'lime', 'olive', 'aqua', 'teal', 'fuchsia', 'cyan', 'magenta', 'beige', 'tan', 'khaki',
                // French colors
                'rouge', 'bleu', 'vert', 'noir', 'blanc', 'jaune', 'rose', 'violet', 'orange', 'marron', 'gris', 'argent', 'or',
                'marine', 'bordeaux', 'citron', 'olive', 'turquoise', 'fuchsia', 'cyan', 'magenta', 'beige',
                // Arabic colors
                'أحمر', 'أزرق', 'أخضر', 'أسود', 'أبيض', 'أصفر', 'وردي', 'بنفسجي', 'برتقالي', 'بني', 'رمادي', 'فضي', 'ذهبي',
                'بحري', 'عنابي', 'زيتوني', 'فيروزي', 'ليموني', 'بيج'
            );

            // Check for exact matches first
            if (in_array($value_lower, $exact_colors)) {
                return true;
            }

            // Check for color words as part of the value (but be more careful)
            foreach ($exact_colors as $color) {
                // Only match if the color word is a significant part of the value
                if (strlen($color) >= 3 && stripos($value_lower, $color) !== false) {
                    // Make sure it's not just a substring of a size-related word
                    $size_indicators = array('xl', 'large', 'small', 'medium', 'grand', 'petit', 'moyen', 'كبير', 'صغير', 'متوسط');
                    $is_size_related = false;
                    foreach ($size_indicators as $size_word) {
                        if (stripos($value_lower, $size_word) !== false) {
                            $is_size_related = true;
                            break;
                        }
                    }
                    if (!$is_size_related) {
                        return true;
                    }
                }
            }

            return false;
        };

        // Helper function to check if a value looks like a size (more precise)
        $is_size_value = function($value) {
            $value_lower = strtolower(trim($value));

            // Exact size matches (more precise)
            $exact_sizes = array(
                // English sizes
                'xs', 'sm', 'md', 'lg', 'xl', 'xxl', 'xxxl', 'small', 'medium', 'large', 'extra', 's', 'm', 'l', '2xl', '3xl', '4xl', '5xl',
                'x-small', 'x-large', 'xx-large', 'xxx-large',
                // French sizes
                'petit', 'moyen', 'grand', 'tres grand', 'extra petit', 'extra grand',
                // Arabic sizes
                'صغير', 'متوسط', 'كبير', 'كبير جدا', 'صغير جدا', 'كبير جداً'
            );

            // Check for exact matches first
            if (in_array($value_lower, $exact_sizes)) {
                return true;
            }

            // Check for size words as part of the value (but be more careful)
            foreach ($exact_sizes as $size) {
                // Only match if the size word is a significant part of the value
                if (strlen($size) >= 1 && stripos($value_lower, $size) !== false) {
                    // Make sure it's not just a substring of a color-related word
                    $color_indicators = array('red', 'blue', 'green', 'black', 'white', 'yellow', 'pink', 'purple', 'orange', 'brown',
                                            'rouge', 'bleu', 'vert', 'noir', 'blanc', 'jaune', 'rose', 'violet',
                                            'أحمر', 'أزرق', 'أخضر', 'أسود', 'أبيض', 'أصفر', 'وردي', 'بنفسجي');
                    $is_color_related = false;
                    foreach ($color_indicators as $color_word) {
                        if (stripos($value_lower, $color_word) !== false) {
                            $is_color_related = true;
                            break;
                        }
                    }
                    if (!$is_color_related) {
                        return true;
                    }
                }
            }

            return false;
        };
          // Define possible attribute names for color (in Arabic, English and French)
        $color_attribute_names = array(
            // English variations
            'attribute_pa_color',
            'attribute_pa_colour',
            'attribute_color',
            'attribute_colour',
            'attribute_pa_colors',
            'attribute_colors',
            'attribute_pa_colours',
            'attribute_colours',
            'pa_color',
            'pa_colour',
            'pa_colors',
            'pa_colours',
            'color',
            'colour',
            'colors',
            'colours',
            // French variations
            'attribute_pa_couleur',
            'attribute_couleur',
            'attribute_pa_couleurs',
            'attribute_couleurs',
            'pa_couleur',
            'pa_couleurs',
            'couleur',
            'couleurs',
            // Arabic variations
            'attribute_pa_لون',
            'attribute_لون',
            'attribute_pa_الوان',
            'attribute_الوان',
            'attribute_pa_ألوان',
            'attribute_ألوان',
            'attribute_pa_الألوان',
            'attribute_الألوان',
            'pa_لون',
            'pa_الوان',
            'pa_ألوان',
            'pa_الألوان',
            'لون',
            'الوان',
            'ألوان',
            'الألوان'
        );

        // Define possible attribute names for size (in Arabic, English and French)
        $size_attribute_names = array(
            // English variations
            'attribute_pa_size',
            'attribute_size',
            'attribute_pa_sizes',
            'attribute_sizes',
            'pa_size',
            'pa_sizes',
            'size',
            'sizes',
            // French variations
            'attribute_pa_taille',
            'attribute_taille',
            'attribute_pa_tailles',
            'attribute_tailles',
            'pa_taille',
            'pa_tailles',
            'taille',
            'tailles',
            // Arabic variations
            'attribute_pa_حجم',
            'attribute_حجم',
            'attribute_pa_قياس',
            'attribute_قياس',
            'attribute_pa_المقاس',
            'attribute_المقاس',
            'attribute_pa_مقاس',
            'attribute_مقاس',
            'attribute_pa_احجام',
            'attribute_احجام',
            'attribute_pa_أحجام',
            'attribute_أحجام',
            'attribute_pa_الأحجام',
            'attribute_الأحجام',
            'pa_حجم',
            'pa_قياس',
            'pa_المقاس',
            'pa_مقاس',
            'pa_احجام',
            'pa_أحجام',
            'pa_الأحجام',
            'حجم',
            'قياس',
            'المقاس',
            'مقاس',
            'احجام',
            'أحجام',
            'الأحجام'
        );

        $color_for_sheets = 'لا يدعم';
        $size_for_sheets = 'لا يدعم';
        $width_for_sheets = 'لا يدعم';

        // Track which attributes were used for color and size to exclude them from width
        $used_color_attr = '';
        $used_size_attr = '';
          // Extract Color attribute with enhanced fallback (prioritized order)
        // Sort color attributes by priority (more specific first)
        $prioritized_color_attrs = array();
        $high_priority_color = array();
        $medium_priority_color = array();
        $low_priority_color = array();

        foreach ($color_attribute_names as $color_attr) {
            if (isset($variation_attributes[$color_attr]) && !empty($variation_attributes[$color_attr])) {
                // High priority: exact color names
                if (in_array($color_attr, array('attribute_pa_color', 'attribute_pa_colour', 'attribute_pa_couleur', 'attribute_pa_لون'))) {
                    $high_priority_color[] = $color_attr;
                }
                // Medium priority: color with prefix
                elseif (strpos($color_attr, 'color') !== false || strpos($color_attr, 'colour') !== false || strpos($color_attr, 'couleur') !== false || strpos($color_attr, 'لون') !== false) {
                    $medium_priority_color[] = $color_attr;
                }
                // Low priority: others
                else {
                    $low_priority_color[] = $color_attr;
                }
            }
        }

        $prioritized_color_attrs = array_merge($high_priority_color, $medium_priority_color, $low_priority_color);

        foreach ($prioritized_color_attrs as $color_attr) {
            $color_value = $variation_attributes[$color_attr];
            $color_for_sheets = $get_display_value($color_attr, $color_value);
            $used_color_attr = $color_attr;
            error_log('RID COD Helper: Found color attribute (priority): ' . $color_attr . ' = ' . $color_for_sheets . ' (from: ' . $color_value . ')');
            break; // Stop after finding first valid color
        }

        // Enhanced fallback: Check for any attribute containing color keywords
        if ($color_for_sheets === 'لا يدعم') {
            foreach ($variation_attributes as $attr_name => $attr_value) {
                if (!empty($attr_value)) {
                    $attr_name_lower = strtolower($attr_name);
                    $normalized_attr = $normalize_attr_name($attr_name);
                    $color_keywords = array('color', 'colour', 'colors', 'colours', 'couleur', 'couleurs', 'لون', 'الوان', 'ألوان', 'الألوان');

                    foreach ($color_keywords as $keyword) {
                        $normalized_keyword = $normalize_attr_name($keyword);
                        if (stripos($attr_name_lower, strtolower($keyword)) !== false || $normalized_attr === $normalized_keyword) {
                            $color_for_sheets = $get_display_value($attr_name, $attr_value);
                            $used_color_attr = $attr_name;
                            error_log('RID COD Helper: Found color via keyword fallback: ' . $attr_name . ' = ' . $color_for_sheets . ' (from: ' . $attr_value . ')');
                            break 2; // Break both loops
                        }
                    }
                }
            }
        }
          // Extract Size attribute with enhanced fallback (prioritized order)
        // Sort size attributes by priority (more specific first)
        $prioritized_size_attrs = array();
        $high_priority_size = array();
        $medium_priority_size = array();
        $low_priority_size = array();

        foreach ($size_attribute_names as $size_attr) {
            if (isset($variation_attributes[$size_attr]) && !empty($variation_attributes[$size_attr])) {
                // High priority: exact size names
                if (in_array($size_attr, array('attribute_pa_size', 'attribute_pa_taille', 'attribute_pa_حجم', 'attribute_pa_قياس'))) {
                    $high_priority_size[] = $size_attr;
                }
                // Medium priority: size with prefix
                elseif (strpos($size_attr, 'size') !== false || strpos($size_attr, 'taille') !== false || strpos($size_attr, 'حجم') !== false || strpos($size_attr, 'قياس') !== false) {
                    $medium_priority_size[] = $size_attr;
                }
                // Low priority: others
                else {
                    $low_priority_size[] = $size_attr;
                }
            }
        }

        $prioritized_size_attrs = array_merge($high_priority_size, $medium_priority_size, $low_priority_size);

        foreach ($prioritized_size_attrs as $size_attr) {
            $size_value = $variation_attributes[$size_attr];
            $size_for_sheets = $get_display_value($size_attr, $size_value);
            $used_size_attr = $size_attr;
            error_log('RID COD Helper: Found size attribute (priority): ' . $size_attr . ' = ' . $size_for_sheets . ' (from: ' . $size_value . ')');
            break; // Stop after finding first valid size
        }

        // Enhanced fallback: Check for any attribute containing size keywords
        if ($size_for_sheets === 'لا يدعم') {
            foreach ($variation_attributes as $attr_name => $attr_value) {
                if (!empty($attr_value)) {
                    $attr_name_lower = strtolower($attr_name);
                    $normalized_attr = $normalize_attr_name($attr_name);
                    $size_keywords = array('size', 'sizes', 'taille', 'tailles', 'قياس', 'حجم', 'المقاس', 'مقاس', 'احجام', 'أحجام', 'الأحجام');

                    foreach ($size_keywords as $keyword) {
                        $normalized_keyword = $normalize_attr_name($keyword);
                        if (stripos($attr_name_lower, strtolower($keyword)) !== false || $normalized_attr === $normalized_keyword) {
                            $size_for_sheets = $get_display_value($attr_name, $attr_value);
                            $used_size_attr = $attr_name;
                            error_log('RID COD Helper: Found size via keyword fallback: ' . $attr_name . ' = ' . $size_for_sheets . ' (from: ' . $attr_value . ')');
                            break 2; // Break both loops
                        }
                    }
                }
            }
        }

        // Extract Width/Other attributes (everything except color and size)
        $other_attributes = array();
        foreach ($variation_attributes as $attr_name => $attr_value) {
            if (!empty($attr_value)) {
                // Skip if this attribute was already used for color or size
                if ($attr_name === $used_color_attr || $attr_name === $used_size_attr) {
                    continue;
                }

                // Skip if this is a color or size attribute
                $is_color = false;
                $is_size = false;

                // Check if it's a color attribute
                foreach ($color_attribute_names as $color_attr) {
                    if ($attr_name === $color_attr) {
                        $is_color = true;
                        break;
                    }
                }

                // Check by keyword if not found in exact names
                if (!$is_color) {
                    $attr_name_lower = strtolower($attr_name);
                    $normalized_attr = $normalize_attr_name($attr_name);
                    $color_keywords = array('color', 'colour', 'colors', 'colours', 'couleur', 'couleurs', 'لون', 'الوان', 'ألوان', 'الألوان');
                    foreach ($color_keywords as $keyword) {
                        $normalized_keyword = $normalize_attr_name($keyword);
                        if (stripos($attr_name_lower, strtolower($keyword)) !== false || $normalized_attr === $normalized_keyword) {
                            $is_color = true;
                            break;
                        }
                    }
                }

                // Check if it's a size attribute
                if (!$is_color) {
                    foreach ($size_attribute_names as $size_attr) {
                        if ($attr_name === $size_attr) {
                            $is_size = true;
                            break;
                        }
                    }

                    // Check by keyword if not found in exact names
                    if (!$is_size) {
                        $attr_name_lower = strtolower($attr_name);
                        $normalized_attr = $normalize_attr_name($attr_name);
                        $size_keywords = array('size', 'sizes', 'taille', 'tailles', 'قياس', 'حجم', 'المقاس', 'مقاس', 'احجام', 'أحجام', 'الأحجام');
                        foreach ($size_keywords as $keyword) {
                            $normalized_keyword = $normalize_attr_name($keyword);
                            if (stripos($attr_name_lower, strtolower($keyword)) !== false || $normalized_attr === $normalized_keyword) {
                                $is_size = true;
                                break;
                            }
                        }
                    }
                }

                // If it's not color or size, add it to other attributes
                if (!$is_color && !$is_size) {
                    $display_value = $get_display_value($attr_name, $attr_value);
                    // Add only the display value without attribute name prefix
                    $other_attributes[] = $display_value;
                    error_log('RID COD Helper: Found other attribute: ' . $attr_name . ' = ' . $display_value . ' (from: ' . $attr_value . ')');
                }
            }
        }

        // Join other attributes for width field
        if (!empty($other_attributes)) {
            $width_for_sheets = implode(' | ', $other_attributes);
        }

        // Final fallback: If still no color or size found, try smart dynamic detection
        if ($color_for_sheets === 'لا يدعم' || $size_for_sheets === 'لا يدعم') {
            error_log('RID COD Helper: Attempting smart dynamic attribute detection...');

            // First pass: Try to identify by attribute name patterns
            foreach ($variation_attributes as $attr_name => $attr_value) {
                if (!empty($attr_value)) {
                    $clean_attr_name = strtolower(str_replace(array('attribute_pa_', 'attribute_', 'pa_'), '', $attr_name));
                    error_log('RID COD Helper: Smart check - Clean name: ' . $clean_attr_name . ' = ' . $attr_value);

                    // Check if attribute name suggests it's a color (prioritize name over value)
                    if ($color_for_sheets === 'لا يدعم') {
                        $name_suggests_color = false;
                        $color_name_hints = array('color', 'colour', 'couleur', 'لون', 'الوان', 'ألوان');

                        foreach ($color_name_hints as $hint) {
                            if (stripos($clean_attr_name, $hint) !== false) {
                                $name_suggests_color = true;
                                break;
                            }
                        }

                        if ($name_suggests_color) {
                            $color_for_sheets = $get_display_value($attr_name, $attr_value);
                            $used_color_attr = $attr_name;
                            error_log('RID COD Helper: Found color via smart name detection: ' . $attr_name . ' = ' . $color_for_sheets . ' (from: ' . $attr_value . ')');
                            continue; // Skip to next attribute
                        }
                    }

                    // Check if attribute name suggests it's a size (prioritize name over value)
                    if ($size_for_sheets === 'لا يدعم') {
                        $name_suggests_size = false;
                        $size_name_hints = array('size', 'taille', 'حجم', 'قياس', 'مقاس');

                        foreach ($size_name_hints as $hint) {
                            if (stripos($clean_attr_name, $hint) !== false) {
                                $name_suggests_size = true;
                                break;
                            }
                        }

                        if ($name_suggests_size) {
                            $size_for_sheets = $get_display_value($attr_name, $attr_value);
                            $used_size_attr = $attr_name;
                            error_log('RID COD Helper: Found size via smart name detection: ' . $attr_name . ' = ' . $size_for_sheets . ' (from: ' . $attr_value . ')');
                            continue; // Skip to next attribute
                        }
                    }
                }
            }

            // Second pass: Only if still not found, try value-based detection (more careful)
            if ($color_for_sheets === 'لا يدعم' || $size_for_sheets === 'لا يدعم') {
                foreach ($variation_attributes as $attr_name => $attr_value) {
                    if (!empty($attr_value) && $attr_name !== $used_color_attr && $attr_name !== $used_size_attr) {
                        $clean_attr_name = strtolower(str_replace(array('attribute_pa_', 'attribute_', 'pa_'), '', $attr_name));

                        // Only check value if name doesn't suggest the opposite type
                        if ($color_for_sheets === 'لا يدعم') {
                            $name_suggests_size = false;
                            $size_name_hints = array('size', 'taille', 'حجم', 'قياس', 'مقاس');
                            foreach ($size_name_hints as $hint) {
                                if (stripos($clean_attr_name, $hint) !== false) {
                                    $name_suggests_size = true;
                                    break;
                                }
                            }

                            if (!$name_suggests_size && $is_color_value($attr_value)) {
                                $color_for_sheets = $get_display_value($attr_name, $attr_value);
                                $used_color_attr = $attr_name;
                                error_log('RID COD Helper: Found color via smart value detection: ' . $attr_name . ' = ' . $color_for_sheets . ' (from: ' . $attr_value . ')');
                            }
                        }

                        if ($size_for_sheets === 'لا يدعم') {
                            $name_suggests_color = false;
                            $color_name_hints = array('color', 'colour', 'couleur', 'لون', 'الوان', 'ألوان');
                            foreach ($color_name_hints as $hint) {
                                if (stripos($clean_attr_name, $hint) !== false) {
                                    $name_suggests_color = true;
                                    break;
                                }
                            }

                            if (!$name_suggests_color && $is_size_value($attr_value)) {
                                $size_for_sheets = $get_display_value($attr_name, $attr_value);
                                $used_size_attr = $attr_name;
                                error_log('RID COD Helper: Found size via smart value detection: ' . $attr_name . ' = ' . $size_for_sheets . ' (from: ' . $attr_value . ')');
                            }
                        }
                    }
                }
            }
        }

        // Additional logging for debugging
        error_log('RID COD Helper: Used color attribute: ' . $used_color_attr);
        error_log('RID COD Helper: Used size attribute: ' . $used_size_attr);
        error_log('RID COD Helper: High priority colors found: ' . implode(', ', $high_priority_color));
        error_log('RID COD Helper: High priority sizes found: ' . implode(', ', $high_priority_size));
        error_log('RID COD Helper: Order item meta used: ' . (!empty($order_item_meta) ? 'Yes' : 'No'));
        error_log('RID COD Helper: Final extracted data - Color: ' . $color_for_sheets . ', Size: ' . $size_for_sheets . ', Width: ' . $width_for_sheets);

        return array(
            'color' => $color_for_sheets,
            'size' => $size_for_sheets,
            'width' => $width_for_sheets
        );
    }

    /**
     * Test function for Google Sheets data extraction (for debugging)
     * Can be called via AJAX to test variation attribute extraction
     */
    public function test_google_sheets_data_extraction() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rid_cod_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'rid-cod')));
        }

        $product_id = isset($_POST['product_id']) ? absint($_POST['product_id']) : 0;
        $variation_id = isset($_POST['variation_id']) ? absint($_POST['variation_id']) : 0;

        if (!$product_id) {
            wp_send_json_error(array('message' => __('Product ID is required', 'rid-cod')));
        }

        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(array('message' => __('Product not found', 'rid-cod')));
        }

        $test_product = $product;

        // If variation ID is provided, use the variation
        if ($variation_id > 0) {
            $variation_product = wc_get_product($variation_id);
            if ($variation_product && $variation_product->is_type('variation')) {
                $test_product = $variation_product;
            }
        }

        // Extract attributes using our helper function
        $extracted_attributes = $this->extract_variation_attributes_for_sheets($test_product, array());

        // Prepare test response
        $response_data = array(
            'product_id' => $product_id,
            'variation_id' => $variation_id,
            'product_type' => $test_product->get_type(),
            'extracted_attributes' => $extracted_attributes,
            'raw_variation_attributes' => $test_product->is_type('variation') ? $test_product->get_variation_attributes() : 'N/A (not a variation)',
            'message' => 'Data extraction test completed successfully'
        );

        wp_send_json_success($response_data);
    }

    /**
     * Test function for sending test data to Google Sheets (Admin only)
     * This function sends sample data to Google Sheets to test the integration
     */
    public function test_google_sheets_send() {
        // Check if user is admin
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied', 'rid-cod')));
        }

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rid_cod_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'rid-cod')));
        }

        // Get Google Sheets settings
        $google_sheets_options = get_option('rid_cod_settings_options', array());
        $apps_script_url = isset($google_sheets_options['google_sheets_apps_script_url']) ? $google_sheets_options['google_sheets_apps_script_url'] : '';

        if (empty($apps_script_url)) {
            wp_send_json_error(array('message' => __('Google Apps Script URL is not configured', 'rid-cod')));
        }

        // Get test data from POST or use defaults
        $test_product_id = isset($_POST['product_id']) ? absint($_POST['product_id']) : 0;
        $test_variation_id = isset($_POST['variation_id']) ? absint($_POST['variation_id']) : 0;

        // Extract attributes if product ID is provided
        $extracted_attributes = array('color' => 'أحمر (اختبار)', 'size' => 'كبير (اختبار)', 'width' => 'عرض: 30سم (اختبار)');

        if ($test_product_id > 0) {
            $test_product = wc_get_product($test_product_id);
            if ($test_variation_id > 0) {
                $variation_product = wc_get_product($test_variation_id);
                if ($variation_product && $variation_product->is_type('variation')) {
                    $test_product = $variation_product;
                }
            }

            if ($test_product) {
                $extracted_attributes = $this->extract_variation_attributes_for_sheets($test_product, array());
            }
        }

        // Prepare test data for Google Sheets
        $test_data = array(
            'product_id'    => $test_product_id ?: 999,
            'product_name'  => 'منتج تجريبي - اختبار Google Sheets',
            'quantity'      => 1,
            'color'         => $extracted_attributes['color'],
            'size'          => $extracted_attributes['size'],
            'width'         => $extracted_attributes['width'],
            'customer_name' => 'عميل تجريبي',
            'phone'         => '0123456789',
            'state'         => 'الجزائر',
            'city'          => 'الجزائر العاصمة',
            'delivery_type' => 'توصيل للمنزل',
            'product_price' => '1000 دج',
            'shipping_cost' => '300 دج',
            'total_price'   => '1300 دج',
        );

        // Add target sheet if configured
        if (!empty($google_sheets_options['google_sheets_sheet_name'])) {
            $test_data['target_sheet'] = $google_sheets_options['google_sheets_sheet_name'];
        }

        // Send test data to Google Sheets
        $args = array(
            'body'        => json_encode($test_data),
            'headers'     => array('Content-Type' => 'application/json'),
            'timeout'     => 30,
            'redirection' => 5,
            'blocking'    => true,
            'httpversion' => '1.0',
            'sslverify'   => true,
            'data_format' => 'body',
        );

        error_log('RID COD Google Sheets Test: Sending test data: ' . print_r($test_data, true));

        $response = wp_remote_post($apps_script_url, $args);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log('RID COD Google Sheets Test Error: ' . $error_message);
            wp_send_json_error(array(
                'message' => 'خطأ في الإرسال: ' . $error_message,
                'test_data' => $test_data
            ));
        } else {
            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);

            if ($response_code >= 200 && $response_code < 400) {
                error_log('RID COD Google Sheets Test Success: Response code: ' . $response_code);
                wp_send_json_success(array(
                    'message' => 'تم إرسال البيانات التجريبية بنجاح إلى Google Sheets',
                    'response_code' => $response_code,
                    'response_body' => $response_body,
                    'test_data' => $test_data
                ));
            } else {
                error_log('RID COD Google Sheets Test Failed: Response code: ' . $response_code . ' Body: ' . $response_body);
                wp_send_json_error(array(
                    'message' => 'فشل في إرسال البيانات - كود الاستجابة: ' . $response_code,
                    'response_code' => $response_code,
                    'response_body' => $response_body,
                    'test_data' => $test_data
                ));
            }
        }
    }

    /**
     * Helper function to get decoded attribute label
     * 
     * @param string $attribute_name The attribute name
     * @return string Decoded and cleaned attribute label
     */
    private function get_decoded_attribute_label($attribute_name) {
        // Get the WooCommerce attribute label
        $attribute_label = wc_attribute_label($attribute_name);
        
        // Use our global decode function to handle URL encoding
        $decoded_label = rid_cod_decode_variation_attribute($attribute_label);
        
        return $decoded_label;
    }

    /**
     * Helper function to validate variation selection
     * @param int $product_id Parent product ID
     * @param int $variation_id Variation ID
     * @param array $submitted_attributes Submitted form attributes
     * @return array|WP_Error Validation result
     */
    function rid_cod_validate_variation_selection($product_id, $variation_id, $submitted_attributes = array()) {
        $parent_product = wc_get_product($product_id);

        if (!$parent_product || !$parent_product->is_type('variable')) {
            return new WP_Error('invalid_product', __('المنتج المحدد ليس متغيراً', 'rid-cod'));
        }

        if (empty($variation_id)) {
            return new WP_Error('no_variation', __('لم يتم اختيار متغير', 'rid-cod'));
        }

        $variation_product = wc_get_product($variation_id);

        if (!$variation_product || !$variation_product->is_type('variation')) {
            return new WP_Error('invalid_variation', __('المتغير المحدد غير صحيح', 'rid-cod'));
        }

        if ($variation_product->get_parent_id() !== $parent_product->get_id()) {
            return new WP_Error('variation_mismatch', __('المتغير لا ينتمي للمنتج المحدد', 'rid-cod'));
        }

        // Get available variations and validate selection
        $available_variations = $parent_product->get_available_variations();
        $found_variation = false;

        foreach ($available_variations as $available_variation) {
            if ($available_variation['variation_id'] == $variation_id) {
                $found_variation = $available_variation;
                break;
            }
        }

        if (!$found_variation) {
            return new WP_Error('variation_not_available', __('هذا المتغير غير متوفر', 'rid-cod'));
        }

        return array(
            'valid' => true,
            'parent_product' => $parent_product,
            'variation_product' => $variation_product,
            'variation_data' => $found_variation
        );
    }

    /**
     * Get states for a specific country
     */
    public function get_states_for_country() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('غير مسموح لك بهذا الإجراء', 'rid-cod')));
        }

        $country_code = sanitize_text_field($_POST['country_code'] ?? '');
        if (empty($country_code)) {
            wp_send_json_error(array('message' => __('كود الدولة مطلوب', 'rid-cod')));
        }

        // Get custom states first, then fallback to default
        $custom_states = get_option("rid_cod_custom_states_{$country_code}", array());

        if (empty($custom_states)) {
            // Get default states from Country Manager
            $default_states = RID_COD_Country_Manager::get_states($country_code);
            $custom_states = $default_states ?: array();
        }

        wp_send_json_success(array('states' => $custom_states));
    }

    /**
     * Save states order for a country
     */
    public function save_states_for_country() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('غير مسموح لك بهذا الإجراء', 'rid-cod')));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'rid_cod_nonce')) {
            wp_send_json_error(array('message' => __('خطأ في التحقق من الأمان', 'rid-cod')));
        }

        $country_code = sanitize_text_field($_POST['country_code'] ?? '');
        $states = $_POST['states'] ?? array();

        if (empty($country_code)) {
            wp_send_json_error(array('message' => __('كود الدولة مطلوب', 'rid-cod')));
        }

        // Sanitize states data
        $sanitized_states = array();
        foreach ($states as $state) {
            $code = sanitize_text_field($state['code'] ?? '');
            $name = sanitize_text_field($state['name'] ?? '');
            if (!empty($code) && !empty($name)) {
                $sanitized_states[$code] = $name;
            }
        }

        // Save custom states
        update_option("rid_cod_custom_states_{$country_code}", $sanitized_states);

        wp_send_json_success(array('message' => __('تم حفظ الولايات بنجاح', 'rid-cod')));
    }

    /**
     * Add a new state
     */
    public function add_state() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('غير مسموح لك بهذا الإجراء', 'rid-cod')));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'rid_cod_nonce')) {
            wp_send_json_error(array('message' => __('خطأ في التحقق من الأمان', 'rid-cod')));
        }

        $country_code = sanitize_text_field($_POST['country_code'] ?? '');
        $state_code = sanitize_text_field($_POST['state_code'] ?? '');
        $state_name = sanitize_text_field($_POST['state_name'] ?? '');

        if (empty($country_code) || empty($state_code) || empty($state_name)) {
            wp_send_json_error(array('message' => __('جميع الحقول مطلوبة', 'rid-cod')));
        }

        // Get current states - initialize with defaults if empty
        $custom_states = get_option("rid_cod_custom_states_{$country_code}", array());

        // If no custom states exist, initialize with default states
        if (empty($custom_states)) {
            $default_states = RID_COD_Country_Manager::get_default_states_by_country($country_code);
            $custom_states = $default_states ?: array();
        }

        // Check if state code already exists
        if (isset($custom_states[$state_code])) {
            wp_send_json_error(array('message' => __('كود الولاية موجود بالفعل', 'rid-cod')));
        }

        // Add new state
        $custom_states[$state_code] = $state_name;
        update_option("rid_cod_custom_states_{$country_code}", $custom_states);

        wp_send_json_success(array(
            'message' => __('تم إضافة الولاية بنجاح', 'rid-cod'),
            'state' => array('code' => $state_code, 'name' => $state_name)
        ));
    }

    /**
     * Edit an existing state
     */
    public function edit_state() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('غير مسموح لك بهذا الإجراء', 'rid-cod')));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'rid_cod_nonce')) {
            wp_send_json_error(array('message' => __('خطأ في التحقق من الأمان', 'rid-cod')));
        }

        $country_code = sanitize_text_field($_POST['country_code'] ?? '');
        $old_code = sanitize_text_field($_POST['old_code'] ?? '');
        $new_code = sanitize_text_field($_POST['new_code'] ?? '');
        $new_name = sanitize_text_field($_POST['new_name'] ?? '');

        if (empty($country_code) || empty($old_code) || empty($new_code) || empty($new_name)) {
            wp_send_json_error(array('message' => __('جميع الحقول مطلوبة', 'rid-cod')));
        }

        // Get current states - initialize with defaults if empty
        $custom_states = get_option("rid_cod_custom_states_{$country_code}", array());

        // If no custom states exist, initialize with default states
        if (empty($custom_states)) {
            $default_states = RID_COD_Country_Manager::get_default_states_by_country($country_code);
            $custom_states = $default_states ?: array();
        }

        // Check if old state exists
        if (!isset($custom_states[$old_code])) {
            wp_send_json_error(array('message' => __('الولاية غير موجودة', 'rid-cod')));
        }

        // If code changed, check if new code already exists
        if ($old_code !== $new_code && isset($custom_states[$new_code])) {
            wp_send_json_error(array('message' => __('كود الولاية الجديد موجود بالفعل', 'rid-cod')));
        }

        // Update shipping costs if state code changed
        if ($old_code !== $new_code) {
            $shipping_costs = get_option("rid_cod_shipping_costs_{$country_code}", array());
            if (isset($shipping_costs[$old_code])) {
                $shipping_costs[$new_code] = $shipping_costs[$old_code];
                unset($shipping_costs[$old_code]);
                update_option("rid_cod_shipping_costs_{$country_code}", $shipping_costs);
            }
        }

        // Remove old state and add new one
        unset($custom_states[$old_code]);
        $custom_states[$new_code] = $new_name;
        update_option("rid_cod_custom_states_{$country_code}", $custom_states);

        wp_send_json_success(array(
            'message' => __('تم تعديل الولاية بنجاح', 'rid-cod'),
            'state' => array('code' => $new_code, 'name' => $new_name)
        ));
    }

    /**
     * Delete a state
     */
    public function delete_state() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('غير مسموح لك بهذا الإجراء', 'rid-cod')));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'rid_cod_nonce')) {
            wp_send_json_error(array('message' => __('خطأ في التحقق من الأمان', 'rid-cod')));
        }

        $country_code = sanitize_text_field($_POST['country_code'] ?? '');
        $state_code = sanitize_text_field($_POST['state_code'] ?? '');

        if (empty($country_code) || empty($state_code)) {
            wp_send_json_error(array('message' => __('كود الدولة والولاية مطلوبان', 'rid-cod')));
        }

        // Get current states - initialize with defaults if empty
        $custom_states = get_option("rid_cod_custom_states_{$country_code}", array());

        // If no custom states exist, initialize with default states
        if (empty($custom_states)) {
            $default_states = RID_COD_Country_Manager::get_default_states_by_country($country_code);
            $custom_states = $default_states ?: array();
        }

        // Check if state exists
        if (!isset($custom_states[$state_code])) {
            wp_send_json_error(array('message' => __('الولاية غير موجودة', 'rid-cod')));
        }

        // Remove state
        unset($custom_states[$state_code]);
        update_option("rid_cod_custom_states_{$country_code}", $custom_states);

        // Also remove associated shipping costs
        $shipping_costs = get_option("rid_cod_shipping_costs_{$country_code}", array());
        if (isset($shipping_costs[$state_code])) {
            unset($shipping_costs[$state_code]);
            update_option("rid_cod_shipping_costs_{$country_code}", $shipping_costs);
        }

        wp_send_json_success(array('message' => __('تم حذف الولاية بنجاح', 'rid-cod')));
    }

    /**
     * Reset states to default for a country
     */
    public function reset_states_to_default() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('غير مسموح لك بهذا الإجراء', 'rid-cod')));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'rid_cod_nonce')) {
            wp_send_json_error(array('message' => __('خطأ في التحقق من الأمان', 'rid-cod')));
        }

        $country_code = sanitize_text_field($_POST['country_code'] ?? '');

        if (empty($country_code)) {
            wp_send_json_error(array('message' => __('كود الدولة مطلوب', 'rid-cod')));
        }

        // Get default states
        $default_states = RID_COD_Country_Manager::get_default_states_by_country($country_code);

        if (empty($default_states)) {
            wp_send_json_error(array('message' => __('لا توجد ولايات افتراضية لهذه الدولة', 'rid-cod')));
        }

        // Save default states as custom states
        update_option("rid_cod_custom_states_{$country_code}", $default_states);

        // Clear any existing shipping costs to reset to defaults
        delete_option("rid_cod_shipping_costs_{$country_code}");

        wp_send_json_success(array(
            'message' => __('تم استعادة الولايات الافتراضية بنجاح', 'rid-cod'),
            'states' => $default_states
        ));
    }
}